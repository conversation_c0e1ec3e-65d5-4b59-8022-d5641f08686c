import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class OtpScreen extends ConsumerStatefulWidget {
  const OtpScreen({
    super.key,
    required this.arg,
  });

  final OtpArg arg;

  @override
  ConsumerState<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends ConsumerState<OtpScreen> {
  final otpC = TextEditingController();
  final otpF = FocusNode();

  @override
  dispose() {
    otpC.dispose();
    otpF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppbar(
        title: "",
      ),
      body: ListView(
        padding: EdgeInsets.only(
          left: Sizer.width(16),
          right: Sizer.width(16),
        ),
        children: [
          imageHelper(
            AppImages.logoBlack,
            height: Sizer.height(48),
          ),
          const YBox(30),
          Text(
            "Verify your number",
            textAlign: TextAlign.center,
            style: AppTypography.text24.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(4),
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              children: [
                TextSpan(
                  text: "We've sent a 4- digit code to ",
                  style: AppTypography.text14.copyWith(
                    color: AppColors.black70,
                  ),
                ),
                TextSpan(
                  text: widget.arg.phoneNo,
                  style: AppTypography.text14.copyWith(
                    color: AppColors.black70,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextSpan(
                  text: " via \nSMS and Whatsapp",
                  style: AppTypography.text14.copyWith(
                    color: AppColors.black70,
                  ),
                ),
              ],
            ),
          ),
          const YBox(40),
          Text(
            "Enter OTP",
            style: AppTypography.text14.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(6),
          Pinput(
            defaultPinTheme: PinInputTheme.defaultPinTheme(),
            followingPinTheme: PinInputTheme.followPinTheme(),
            length: 4,
            controller: otpC,
            focusNode: otpF,
            showCursor: true,
            // onChanged: (value) => vm..reBuildUI(),
            onCompleted: (pin) {
              _verifyOtp();
            },
          ),
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: Sizer.height(30),
            ),
            child: const ResendCode(),
          ),
          // const YBox(20),
          // Text(
          //   "Tap here to resend OTP",
          //   textAlign: TextAlign.center,
          //   style: AppTypography.text14.copyWith(
          //     fontWeight: FontWeight.w600,
          //   ),
          // )
        ],
      ),
      bottomSheet: Container(
        color: Colors.white,
        padding: EdgeInsets.only(
          left: Sizer.width(16),
          right: Sizer.width(16),
          top: Sizer.height(5),
          bottom: Sizer.height(30),
        ),
        child: CustomBtn.solid(
          onTap: () {
            _verifyOtp();
          },
          online: otpC.text.trim().length == 4,
          isLoading: ref.watch(authVm).isBusy,
          text: "Verify",
        ),
      ),
    );
  }

  _verifyOtp() async {
    final res = await ref.read(authVm.notifier).verifyOTP(otpC.text.trim());
    handleApiResponse(
      response: res,
      onSuccess: () {
        Navigator.pushReplacementNamed(context, RoutePath.bottomNavScreen);
      },
    );
  }
}
