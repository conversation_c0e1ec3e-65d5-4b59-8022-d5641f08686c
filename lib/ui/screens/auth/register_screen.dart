import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:flutter/gestures.dart';

class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _fullNameC = TextEditingController();
  final _emailC = TextEditingController();
  final _passwordC = TextEditingController();
  final _phoneC = TextEditingController();
  final _dobC = TextEditingController();

  final _fullNameF = FocusNode();
  final _emailF = FocusNode();
  final _passwordF = FocusNode();
  final _phoneF = FocusNode();
  final _dobF = FocusNode();

  DateTime? _selectedDOB;
  String? _ageValidationError;

  // Minimum age requirement (18 years)
  static const int _minimumAge = 18;

  @override
  void dispose() {
    _fullNameC.dispose();
    _emailC.dispose();
    _passwordC.dispose();
    _phoneC.dispose();
    _dobC.dispose();

    _fullNameF.dispose();
    _emailF.dispose();
    _passwordF.dispose();
    _phoneF.dispose();
    _dobF.dispose();

    super.dispose();
  }

  /// Calculates age based on date of birth
  int _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;

    // Check if birthday hasn't occurred this year yet
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }

    return age;
  }

  /// Validates if user meets minimum age requirement
  bool _isAgeValid() {
    if (_selectedDOB == null) return false;
    return _calculateAge(_selectedDOB!) >= _minimumAge;
  }

  /// Updates age validation error message
  void _updateAgeValidation() {
    if (_selectedDOB == null) {
      _ageValidationError = null;
    } else if (!_isAgeValid()) {
      _ageValidationError =
          'You must be at least $_minimumAge years old to register';
    } else {
      _ageValidationError = null;
    }
  }

  bool get btnIsActive {
    return _fullNameC.text.isNotEmpty &&
        _emailC.text.isNotEmpty &&
        // _passwordC.text.isNotEmpty &&
        _phoneC.text.isNotEmpty &&
        _selectedDOB != null &&
        _isAgeValid();
  }

  @override
  Widget build(BuildContext context) {
    final authRef = ref.watch(authVm);
    return BusyOverlay(
      show: authRef.isBusy,
      child: Scaffold(
        appBar: const CustomAppbar(
          title: "",
        ),
        body: ListView(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
          ),
          children: [
            imageHelper(
              AppImages.logoBlack,
              height: Sizer.height(48),
            ),
            const YBox(24),
            CustomTextField(
              controller: _fullNameC,
              focusNode: _fullNameF,
              labelText: "Full name",
              showLabelHeader: true,
              borderRadius: 0,
              onChanged: (p0) => setState(() {}),
            ),
            const YBox(16),
            CustomTextField(
              controller: _emailC,
              focusNode: _emailF,
              labelText: "Email",
              keyboardType: KeyboardType.email,
              showLabelHeader: true,
              borderRadius: 0,
              onChanged: (p0) => setState(() {}),
            ),
            const YBox(16),
            CustomTextField(
              controller: _phoneC,
              focusNode: _phoneF,
              hintText: "enter phone number",
              labelText: "Phone number",
              showLabelHeader: true,
              borderRadius: 0,
              keyboardType: KeyboardType.phone,
              onChanged: (p0) => setState(() {}),
              prefixIcon: Padding(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(10)),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "NG",
                      style: AppTypography.text16.copyWith(),
                    ),
                    Icon(
                      Icons.keyboard_arrow_down,
                      size: Sizer.height(24),
                      color: AppColors.black70,
                    )
                  ],
                ),
              ),
            ),
            const YBox(16),
            // CustomTextField(
            //   controller: _passwordC,
            //   focusNode: _passwordF,
            //   labelText: "Password",
            //   keyboardType: KeyboardType.email,
            //   showLabelHeader: true,
            //   borderRadius: 0,
            //   isPassword: true,
            //   onChanged: (p0) => setState(() {}),
            // ),
            // const YBox(16),
            CustomTextField(
              controller: _dobC,
              focusNode: _dobF,
              hintText: "Select date",
              labelText: "Birthday",
              showLabelHeader: true,
              isReadOnly: true,
              borderRadius: 0,
              errorText: _ageValidationError,
              onChanged: (p0) => setState(() {}),
              suffixIcon: Icon(
                Iconsax.calendar_1,
                size: Sizer.height(20),
                color: AppColors.black70,
              ),
              onTap: () {
                CustomCupertinoDatePicker(
                  context: context,
                  maximumDate: DateTime.now(),
                  onDateTimeChanged: (dateTime) {
                    _selectedDOB = dateTime;
                    _dobC.text = AppUtils.dayWithSuffixMonthAndYear(dateTime);
                    _updateAgeValidation();

                    setState(() {});
                  },
                ).show();
              },
            ),
            const YBox(24),
            CustomBtn.solid(
              onTap: () async {
                FocusScope.of(context).unfocus();

                // Validate age before proceeding
                if (_selectedDOB == null) {
                  FlushBarToast.fLSnackBar(
                    snackBarType: SnackBarType.warning,
                    message: 'Please select your date of birth',
                  );
                  return;
                }

                if (!_isAgeValid()) {
                  FlushBarToast.fLSnackBar(
                    snackBarType: SnackBarType.warning,
                    message:
                        'You must be at least $_minimumAge years old to register',
                  );
                  return;
                }

                final r = await ref.read(authVm.notifier).createAccount(
                      firstName: _fullNameC.text.trim(),
                      lastName: _fullNameC.text.trim(),
                      email: _emailC.text.trim(),
                      phone: _phoneC.text.trim(),
                      password: _passwordC.text.trim(),
                      dob: _selectedDOB?.toIso8601String() ?? "",
                    );

                handleApiResponse(
                  response: r,
                  onSuccess: () {
                    Navigator.pushNamed(
                      context,
                      RoutePath.otpScreen,
                      arguments: OtpArg(
                        phoneNo: _phoneC.text.trim(),
                      ),
                    );
                    // Navigator.pushNamed(context, RoutePath.bottomNavScreen);
                  },
                );
              },
              online: btnIsActive,
              text: "Continue",
            ),
            // const YBox(24),
            // Text(
            //   "OR",
            //   textAlign: TextAlign.center,
            //   style: AppTypography.text14.copyWith(
            //     color: AppColors.black70,
            //     fontWeight: FontWeight.w600,
            //   ),
            // ),
            // const YBox(24),
            // SocialBtn(
            //   iconPath: AppSvgs.google,
            //   btnText: "Sign up with Google",
            //   onTap: () {},
            // ),
            // const YBox(12),
            // SocialBtn(
            //   iconPath: AppSvgs.apple,
            //   btnText: "Sign up with Apple",
            //   onTap: () {},
            // ),
            const YBox(20),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "Already have an account? ",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: " Login",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'GeneralSans',
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Navigator.pop(context);
                      },
                  ),
                ],
              ),
            ),
            const YBox(80),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "By continuing, you agree to our ",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: "Terms of Use",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: " and acknowlegde that you have read our ",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: "Privacy Policy",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
