import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Age Validation Tests', () {
    // Helper function to calculate age (same logic as in register_screen.dart)
    int calculateAge(DateTime birthDate) {
      final now = DateTime.now();
      int age = now.year - birthDate.year;
      
      // Check if birthday hasn't occurred this year yet
      if (now.month < birthDate.month || 
          (now.month == birthDate.month && now.day < birthDate.day)) {
        age--;
      }
      
      return age;
    }

    // Helper function to check if age is valid (18 or older)
    bool isAgeValid(DateTime? birthDate) {
      if (birthDate == null) return false;
      return calculateAge(birthDate) >= 18;
    }

    test('should calculate age correctly for someone exactly 18 years old', () {
      final now = DateTime.now();
      final eighteenYearsAgo = DateTime(now.year - 18, now.month, now.day);
      
      expect(calculateAge(eighteenYearsAgo), equals(18));
      expect(isAgeValid(eighteenYearsAgo), isTrue);
    });

    test('should calculate age correctly for someone under 18', () {
      final now = DateTime.now();
      final seventeenYearsAgo = DateTime(now.year - 17, now.month, now.day);
      
      expect(calculateAge(seventeenYearsAgo), equals(17));
      expect(isAgeValid(seventeenYearsAgo), isFalse);
    });

    test('should calculate age correctly for someone over 18', () {
      final now = DateTime.now();
      final twentyYearsAgo = DateTime(now.year - 20, now.month, now.day);
      
      expect(calculateAge(twentyYearsAgo), equals(20));
      expect(isAgeValid(twentyYearsAgo), isTrue);
    });

    test('should handle birthday not yet occurred this year', () {
      final now = DateTime.now();
      // Create a date 18 years ago but with a future month/day
      final futureMonth = now.month == 12 ? 1 : now.month + 1;
      final futureYear = now.month == 12 ? now.year - 17 : now.year - 18;
      final birthDate = DateTime(futureYear, futureMonth, now.day);
      
      final age = calculateAge(birthDate);
      // Should be 17 if birthday hasn't occurred yet this year
      expect(age, equals(17));
      expect(isAgeValid(birthDate), isFalse);
    });

    test('should return false for null birth date', () {
      expect(isAgeValid(null), isFalse);
    });

    test('should handle edge case - birthday today exactly 18 years ago', () {
      final now = DateTime.now();
      final exactlyEighteenYearsAgo = DateTime(now.year - 18, now.month, now.day);
      
      expect(calculateAge(exactlyEighteenYearsAgo), equals(18));
      expect(isAgeValid(exactlyEighteenYearsAgo), isTrue);
    });

    test('should handle edge case - birthday tomorrow exactly 18 years ago', () {
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));
      final eighteenYearsAgoTomorrow = DateTime(
        tomorrow.year - 18, 
        tomorrow.month, 
        tomorrow.day
      );
      
      // Should still be 17 since birthday is tomorrow
      expect(calculateAge(eighteenYearsAgoTomorrow), equals(17));
      expect(isAgeValid(eighteenYearsAgoTomorrow), isFalse);
    });
  });
}
